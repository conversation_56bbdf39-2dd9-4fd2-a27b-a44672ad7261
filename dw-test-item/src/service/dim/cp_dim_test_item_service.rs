use std::collections::HashMap;
use std::error::Error;

use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::config::DwTestItemConfig;
use crate::service::dim::test_item_service::TestItemService;
use crate::service::dim::test_program_test_item_service::TestProgramTestItemService;

/// CpDimTestItemService handles CP (Contact Probe) stage DIM layer processing
/// Corresponds to CpDimTestItemService.scala
#[derive(Debug, Clone)]
pub struct CpDimTestItemService {
    properties: DwTestItemConfig,
}

impl CpDimTestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate CP DIM layer data
    /// Corresponds to calculate method in CpDimTestItemService.scala
    pub async fn calculate(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        log::info!("开始计算 CP DIM 层数据...");

        let dim_db_name = &self.properties.dim_db_name;

        // 1. 计算 DIM TestItem
        log::info!("开始计算 DIM TestItem...");
        let test_item_service = TestItemService::new(self.properties.clone());
        let test_items = test_item_service
            .calculate_test_item(sub_test_item, file_detail_map, dim_db_name)
            .await?;
        log::info!("DIM TestItem 计算完成，共生成 {} 条记录", test_items.len());

        // 2. 计算 DIM TestProgramTestItem
        log::info!("开始计算 DIM TestProgramTestItem...");
        let test_program_test_item_service = TestProgramTestItemService::new(self.properties.clone());
        test_program_test_item_service
            .calculate_test_program_test_item_from_test_item(&test_items, dim_db_name)
            .await?;
        log::info!("DIM TestProgramTestItem 计算完成");

        log::info!("CP DIM 层数据计算完成");
        Ok(())
    }
}
