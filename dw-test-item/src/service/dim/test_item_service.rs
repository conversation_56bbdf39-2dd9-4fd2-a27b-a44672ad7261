use chrono::{DateTime, Utc};
use rayon::prelude::*;
use std::collections::HashMap;
use std::sync::Arc;

use crate::config::DwTestItemConfig;
use common::dim::sink::test_item_handler::TestItemHandler;
use common::dto::dim::DimTestItemRow;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::model::constant::{EMPTY, SYSTEM};
use common::utils::decimal::Decimal38_18;

/// TestItemService handles DIM layer TestItem processing
/// Corresponds to TestItemService.scala
#[derive(Debug, Clone)]
pub struct TestItemService {
    properties: DwTestItemConfig,
}

impl TestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate TestItem from SubTestItemDetail
    /// Corresponds to calculateTestItem method in Scala
    pub async fn calculate_test_item(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        dim_db_name: &str,
    ) -> Result<Vec<DimTestItemRow>, Box<dyn std::error::Error + Send + Sync>> {
        log::info!("开始计算 DIM TestItem...");

        // Transform SubTestItemDetail to DimTestItemRow
        let test_items: Vec<DimTestItemRow> = sub_test_item
            .par_iter()
            .flatten()
            .into_par_iter()
            .filter_map(|elem| {
                if let Some(file_detail) = file_detail_map.get(&elem.FILE_ID?) {
                    Some(DimTestItemRow::build_test_item(elem, file_detail))
                } else {
                    log::warn!("File detail not found for FILE_ID: {:?}", elem.FILE_ID);
                    None
                }
            })
            .collect();
        log::info!("dim test_items 转换完成.");
        // Group by key and reduce (calculate time)
        let grouped_items = self.group_and_reduce(test_items);
        log::info!("dim test_items 分组完成.");
        let rows_count = grouped_items.len();

        // Write to ClickHouse
        self.write_to_clickhouse(&grouped_items, dim_db_name).await?;

        log::info!("DIM TestItem 计算完成，共处理 {} 条记录", rows_count);
        Ok(grouped_items)
    }

    /// Group DimTestItemRow by key and reduce (calculate time)
    /// Corresponds to groupByKey and reduceGroups logic in Scala
    fn group_and_reduce(&self, items: Vec<DimTestItemRow>) -> Vec<DimTestItemRow> {
        // For now, we'll implement a simple grouping by a composite key
        // In a real implementation, you might want to create a proper key struct
        let mut grouped: HashMap<String, Vec<DimTestItemRow>> = HashMap::new();

        // Group by key (simplified - using a string key for now)
        for item in items {
            let key = format!(
                "{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}:{}",
                item.CUSTOMER.as_ref(),
                item.SUB_CUSTOMER.as_ref(),
                item.TEST_AREA.as_ref(),
                item.FACTORY.as_ref(),
                item.DEVICE_ID.as_ref(),
                item.TEST_ITEM.as_ref(),
                item.LOT_ID.as_ref(),
                item.LOT_TYPE.as_ref(),
                item.WAFER_NO_KEY.as_ref(),
                item.SBLOT_ID.as_ref(),
                item.SITE.unwrap_or(0),
                item.SBIN_NUM.unwrap_or(0),
                item.HBIN_NUM.unwrap_or(0),
                item.TEST_STAGE.as_ref(),
                item.TEST_PROGRAM.as_ref(),
                item.TEST_TEMPERATURE.as_ref(),
                item.TESTER_NAME.as_ref(),
                item.PROBER_HANDLER_ID.as_ref(),
                item.PROBECARD_LOADBOARD_ID.as_ref()
            );
            grouped.entry(key).or_insert_with(Vec::new).push(item);
        }

        // Reduce each group (calculate time)
        grouped
            .into_values()
            .map(|group| {
                group
                    .into_iter()
                    .reduce(|mut accumulate, right| {
                        self.calculate_test_item_ts(&mut accumulate, &right);
                        accumulate
                    })
                    .unwrap()
            })
            .collect()
    }

    /// Calculate time for DimTestItemRow
    /// Corresponds to calculateTestItemTs method in Scala
    fn calculate_test_item_ts(&self, accumulate: &mut DimTestItemRow, right: &DimTestItemRow) {
        // Calculate time - take min start time and max end time
        if let (Some(acc_start), Some(right_start)) = (accumulate.START_TIME, right.START_TIME) {
            accumulate.START_TIME = Some(acc_start.min(right_start));
        } else if right.START_TIME.is_some() {
            accumulate.START_TIME = right.START_TIME;
        }

        if let (Some(acc_end), Some(right_end)) = (accumulate.END_TIME, right.END_TIME) {
            accumulate.END_TIME = Some(acc_end.max(right_end));
        } else if right.END_TIME.is_some() {
            accumulate.END_TIME = right.END_TIME;
        }

        // Update time keys
        if let Some(start_time) = accumulate.START_TIME {
            accumulate.START_HOUR_KEY = common::utils::date::get_day_hour(start_time).into();
            accumulate.START_DAY_KEY = common::utils::date::get_day(start_time).into();
        }

        if let Some(end_time) = accumulate.END_TIME {
            accumulate.END_HOUR_KEY = common::utils::date::get_day_hour(end_time).into();
            accumulate.END_DAY_KEY = common::utils::date::get_day(end_time).into();
        }
    }

    /// Write TestItem rows to ClickHouse
    async fn write_to_clickhouse(
        &self,
        rows: &Vec<DimTestItemRow>,
        dim_db_name: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if rows.is_empty() {
            log::info!("没有 TestItem 数据需要写入");
            return Ok(());
        }

        let rows_count = rows.len();
        let ck_config = self.properties.get_ck_config(dim_db_name);
        let handler = TestItemHandler::new(dim_db_name.to_string(), self.properties.insert_cluster_table);

        handler.write_to_ck_generic(rows, ck_config).await?;

        log::info!("成功写入 {} 条 TestItem 记录到 ClickHouse", rows_count);
        Ok(())
    }
}
